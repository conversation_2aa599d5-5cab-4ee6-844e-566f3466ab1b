-------------------------------------------------------------------------------
Test set: com.gl.service.installationPackage.service.InstallationPackageServiceTest
-------------------------------------------------------------------------------
Tests run: 23, Failures: 2, Errors: 4, Skipped: 0, Time elapsed: 2.325 s <<< FAILURE! - in com.gl.service.installationPackage.service.InstallationPackageServiceTest
testList_DatabaseException  Time elapsed: 0.062 s  <<< FAILURE!
org.mockito.exceptions.verification.opentest4j.ArgumentsAreDifferent: 

Argument(s) are different! Wanted:
jdbcTemplate.queryForObject(
    "select count(1) from (select 
p.id,
p.version_name,
p.remark,
p.package_url,
p.create_time,
count(pl.id) updateDeviceNum, 
group_concat(pl.device_id) deviceIds from dub_installation_package p left join dub_installation_package_log pl on pl.package_id = p.id where 1=1  and ( p.version_name like ? ) GROUP BY p.id  ) t",
    class java.lang.Long,
    ["%test%"]
);
-> at org.springframework.jdbc.core.JdbcTemplate.queryForObject(JdbcTemplate.java:906)
Actual invocations have different arguments:
jdbcTemplate.queryForObject(
    "select count(1) from (select 
p.id,
p.version_name,
p.remark,
p.package_url,
p.create_time,
count(pl.id) updateDeviceNum, 
group_concat(pl.device_id) deviceIds from dub_installation_package p left join dub_installation_package_log pl on pl.package_id = p.id where 1=1  and ( p.version_name like ? ) GROUP BY p.id  ) t",
    class java.lang.Long,
    "%test%"
);
-> at com.gl.service.installationPackage.service.InstallationPackageService.list(InstallationPackageService.java:59)

	at com.gl.service.installationPackage.service.InstallationPackageServiceTest.testList_DatabaseException(InstallationPackageServiceTest.java:627)

testList_EmptyResult_Success  Time elapsed: 0.006 s  <<< ERROR!
org.mockito.exceptions.misusing.PotentialStubbingProblem: 

Strict stubbing argument mismatch. Please check:
 - this invocation of 'queryForObject' method:
    jdbcTemplate.queryForObject(
    "select count(1) from (select 
p.id,
p.version_name,
p.remark,
p.package_url,
p.create_time,
count(pl.id) updateDeviceNum, 
group_concat(pl.device_id) deviceIds from dub_installation_package p left join dub_installation_package_log pl on pl.package_id = p.id where 1=1  and ( p.version_name like ? ) GROUP BY p.id  ) t",
    class java.lang.Long,
    "%test%"
);
    -> at com.gl.service.installationPackage.service.InstallationPackageService.list(InstallationPackageService.java:59)
 - has following stubbing(s) with different arguments:
    1. jdbcTemplate.queryForObject(
    null,
    null,
    null
);
      -> at com.gl.service.installationPackage.service.InstallationPackageServiceTest.testList_EmptyResult_Success(InstallationPackageServiceTest.java:214)
Typically, stubbing argument mismatch indicates user mistake when writing tests.
Mockito fails early so that you can debug potential problem easily.
However, there are legit scenarios when this exception generates false negative signal:
  - stubbing the same method multiple times using 'given().will()' or 'when().then()' API
    Please use 'will().given()' or 'doReturn().when()' API for stubbing.
  - stubbed method is intentionally invoked with different arguments by code under test
    Please use default or 'silent' JUnit Rule (equivalent of Strictness.LENIENT).
For more information see javadoc for PotentialStubbingProblem class.
	at com.gl.service.installationPackage.service.InstallationPackageServiceTest.testList_EmptyResult_Success(InstallationPackageServiceTest.java:218)

testList_ExportMode_Success  Time elapsed: 0.007 s  <<< ERROR!
org.mockito.exceptions.misusing.PotentialStubbingProblem: 

Strict stubbing argument mismatch. Please check:
 - this invocation of 'queryForObject' method:
    jdbcTemplate.queryForObject(
    "select count(1) from (select 
p.id,
p.version_name,
p.remark,
p.package_url,
p.create_time,
count(pl.id) updateDeviceNum, 
group_concat(pl.device_id) deviceIds from dub_installation_package p left join dub_installation_package_log pl on pl.package_id = p.id where 1=1  and ( p.version_name like ? ) GROUP BY p.id  ) t",
    class java.lang.Long,
    "%test%"
);
    -> at com.gl.service.installationPackage.service.InstallationPackageService.list(InstallationPackageService.java:59)
 - has following stubbing(s) with different arguments:
    1. jdbcTemplate.queryForObject(
    null,
    null,
    null
);
      -> at com.gl.service.installationPackage.service.InstallationPackageServiceTest.testList_ExportMode_Success(InstallationPackageServiceTest.java:274)
Typically, stubbing argument mismatch indicates user mistake when writing tests.
Mockito fails early so that you can debug potential problem easily.
However, there are legit scenarios when this exception generates false negative signal:
  - stubbing the same method multiple times using 'given().will()' or 'when().then()' API
    Please use 'will().given()' or 'doReturn().when()' API for stubbing.
  - stubbed method is intentionally invoked with different arguments by code under test
    Please use default or 'silent' JUnit Rule (equivalent of Strictness.LENIENT).
For more information see javadoc for PotentialStubbingProblem class.
	at com.gl.service.installationPackage.service.InstallationPackageServiceTest.testList_ExportMode_Success(InstallationPackageServiceTest.java:293)

testList_NullDto_PaginationMode_ThrowsException  Time elapsed: 0.005 s  <<< FAILURE!
org.opentest4j.AssertionFailedError: dto为null且使用分页模式时应抛出NullPointerException ==> Unexpected exception type thrown ==> expected: <java.lang.NullPointerException> but was: <org.mockito.exceptions.misusing.PotentialStubbingProblem>
	at com.gl.service.installationPackage.service.InstallationPackageServiceTest.testList_NullDto_PaginationMode_ThrowsException(InstallationPackageServiceTest.java:253)
Caused by: org.mockito.exceptions.misusing.PotentialStubbingProblem: 

Strict stubbing argument mismatch. Please check:
 - this invocation of 'queryForObject' method:
    jdbcTemplate.queryForObject(
    "select count(1) from (select 
p.id,
p.version_name,
p.remark,
p.package_url,
p.create_time,
count(pl.id) updateDeviceNum, 
group_concat(pl.device_id) deviceIds from dub_installation_package p left join dub_installation_package_log pl on pl.package_id = p.id where 1=1 GROUP BY p.id  ) t",
    class java.lang.Long
);
    -> at com.gl.service.installationPackage.service.InstallationPackageService.list(InstallationPackageService.java:59)
 - has following stubbing(s) with different arguments:
    1. jdbcTemplate.queryForObject(
    null,
    null,
    null
);
      -> at com.gl.service.installationPackage.service.InstallationPackageServiceTest.testList_NullDto_PaginationMode_ThrowsException(InstallationPackageServiceTest.java:249)
Typically, stubbing argument mismatch indicates user mistake when writing tests.
Mockito fails early so that you can debug potential problem easily.
However, there are legit scenarios when this exception generates false negative signal:
  - stubbing the same method multiple times using 'given().will()' or 'when().then()' API
    Please use 'will().given()' or 'doReturn().when()' API for stubbing.
  - stubbed method is intentionally invoked with different arguments by code under test
    Please use default or 'silent' JUnit Rule (equivalent of Strictness.LENIENT).
For more information see javadoc for PotentialStubbingProblem class.
	at com.gl.service.installationPackage.service.InstallationPackageServiceTest.lambda$testList_NullDto_PaginationMode_ThrowsException$0(InstallationPackageServiceTest.java:254)
	at com.gl.service.installationPackage.service.InstallationPackageServiceTest.testList_NullDto_PaginationMode_ThrowsException(InstallationPackageServiceTest.java:253)

testList_WithoutSearchCondition_Success  Time elapsed: 0.002 s  <<< ERROR!
org.mockito.exceptions.misusing.PotentialStubbingProblem: 

Strict stubbing argument mismatch. Please check:
 - this invocation of 'queryForObject' method:
    jdbcTemplate.queryForObject(
    "select count(1) from (select 
p.id,
p.version_name,
p.remark,
p.package_url,
p.create_time,
count(pl.id) updateDeviceNum, 
group_concat(pl.device_id) deviceIds from dub_installation_package p left join dub_installation_package_log pl on pl.package_id = p.id where 1=1 GROUP BY p.id  ) t",
    class java.lang.Long
);
    -> at com.gl.service.installationPackage.service.InstallationPackageService.list(InstallationPackageService.java:59)
 - has following stubbing(s) with different arguments:
    1. jdbcTemplate.queryForObject(
    null,
    null,
    null
);
      -> at com.gl.service.installationPackage.service.InstallationPackageServiceTest.testList_WithoutSearchCondition_Success(InstallationPackageServiceTest.java:167)
Typically, stubbing argument mismatch indicates user mistake when writing tests.
Mockito fails early so that you can debug potential problem easily.
However, there are legit scenarios when this exception generates false negative signal:
  - stubbing the same method multiple times using 'given().will()' or 'when().then()' API
    Please use 'will().given()' or 'doReturn().when()' API for stubbing.
  - stubbed method is intentionally invoked with different arguments by code under test
    Please use default or 'silent' JUnit Rule (equivalent of Strictness.LENIENT).
For more information see javadoc for PotentialStubbingProblem class.
	at com.gl.service.installationPackage.service.InstallationPackageServiceTest.testList_WithoutSearchCondition_Success(InstallationPackageServiceTest.java:187)

testList_WithSearchCondition_Success  Time elapsed: 0.003 s  <<< ERROR!
org.mockito.exceptions.misusing.PotentialStubbingProblem: 

Strict stubbing argument mismatch. Please check:
 - this invocation of 'queryForObject' method:
    jdbcTemplate.queryForObject(
    "select count(1) from (select 
p.id,
p.version_name,
p.remark,
p.package_url,
p.create_time,
count(pl.id) updateDeviceNum, 
group_concat(pl.device_id) deviceIds from dub_installation_package p left join dub_installation_package_log pl on pl.package_id = p.id where 1=1  and ( p.version_name like ? ) GROUP BY p.id  ) t",
    class java.lang.Long,
    "%test%"
);
    -> at com.gl.service.installationPackage.service.InstallationPackageService.list(InstallationPackageService.java:59)
 - has following stubbing(s) with different arguments:
    1. jdbcTemplate.queryForObject(
    null,
    null,
    null
);
      -> at com.gl.service.installationPackage.service.InstallationPackageServiceTest.testList_WithSearchCondition_Success(InstallationPackageServiceTest.java:105)
Typically, stubbing argument mismatch indicates user mistake when writing tests.
Mockito fails early so that you can debug potential problem easily.
However, there are legit scenarios when this exception generates false negative signal:
  - stubbing the same method multiple times using 'given().will()' or 'when().then()' API
    Please use 'will().given()' or 'doReturn().when()' API for stubbing.
  - stubbed method is intentionally invoked with different arguments by code under test
    Please use default or 'silent' JUnit Rule (equivalent of Strictness.LENIENT).
For more information see javadoc for PotentialStubbingProblem class.
	at com.gl.service.installationPackage.service.InstallationPackageServiceTest.testList_WithSearchCondition_Success(InstallationPackageServiceTest.java:126)

